{"name": "kuaidihelp_city", "version": "2.8.6", "description": "微商单号使用记录查询参数缺失修复", "private": true, "scripts": {"start": "umi dev", "build": "umi build", "build:debug": "cross-env DEBUG_DEV=OPEN umi build", "deploy": "npm install --production && kbh5-deploy", "analyze": "cross-env ANALYZE=1 umi build", "lint": "npm run lint:js && npm run lint:style && npm run lint:prettier", "lint-staged": "lint-staged", "lint-staged:js": "eslint --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint --fix --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src && npm run lint:style", "lint:js": "eslint --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src", "lint:prettier": "prettier --check \"src/**/*\" --end-of-line auto", "lint:style": "stylelint --fix \"src/**/*.less\" --syntax less", "prettier": "prettier --write '**/*.{js,jsx,tsx,ts,less,md,json}'", "commit": "git add . && git status -s && cz", "prepare": "husky install", "husky:init": "npm run husky:pre-commit && npm run husky:commit-msg", "husky:pre-commit": "husky add .husky/pre-commit 'npm run lint-staged'", "husky:commit-msg": "husky add .husky/commit-msg 'npx --no-install commitlint --edit \"$1\"'"}, "lint-staged": {"**/*.less": "stylelint --fix --syntax less", "**/*.{js,jsx,ts,tsx}": "npm run lint-staged:js", "**/*.{js,jsx,ts,tsx,html,ejs,less}": ["kblint --fix", "git add"], "**/*.{js,jsx,tsx,ts,less,md,json}": ["prettier --write"]}, "dependencies": {"@ant-design/icons": "^5.3.0", "@antv/data-set": "^0.9.0", "@babel/polyfill": "^7.8.0", "@turf/turf": "^6.5.0", "@umijs/hooks": "^1.9.3", "ahooks": "^2.9.3", "antd": "^3.19.7", "babel-plugin-dva-hmr": "^0.4.1", "babel-plugin-import": "^1.6.7", "babel-plugin-module-resolver": "^3.1.1", "babel-plugin-transform-decorators-legacy": "^1.3.4", "barcode": "^0.1.0", "bizcharts": "^3.4.3", "bizcharts-plugin-slider": "^2.0.1", "braft-editor": "^2.3.9", "braft-finder": "0.0.21", "braft-utils": "^3.0.12", "classnames": "^2.2.5", "core-js": "^2.6.11", "crypto-js": "^4.0.0", "dexie": "^3.2.7", "dexie-react-hooks": "^1.1.7", "enquire-js": "^0.2.1", "file-saver": "^2.0.5", "husky": "^7.0.4", "immutability-helper": "^2.9.1", "is-promise": "^4.0.0", "js-cookie": "^3.0.5", "jsbarcode": "^3.11.0", "jszip": "^3.10.1", "kbh5-deploy": "0.0.6", "lodash": "^4.17.10", "lodash-decorators": "^6.0.0", "memoize-one": "^5.0.0", "mockjs": "^1.1.0", "moment": "^2.19.3", "numeral": "^2.0.6", "omit.js": "^1.0.0", "path-to-regexp": "^2.1.0", "prop-types": "^15.5.10", "qrcode.react": "^3.1.0", "qs": "^6.5.0", "react": "^16.13.1", "react-amap": "^1.2.7", "react-container-query": "^0.11.0", "react-countup": "^4.4.0", "react-dnd": "^14.0.3", "react-dnd-html5-backend": "^14.0.1", "react-document-title": "^2.0.3", "react-fittext": "^1.0.0", "react-iframe": "^1.3.3", "react-infinite-scroller": "^1.2.6", "react-pdf-js": "^5.1.0", "react-photo-view": "^0.5.2", "react-player": "^2.9.0", "react-script-hook": "^1.7.2", "react-zoom-pan-pinch": "^3.6.1", "setprototypeof": "^1.1.0", "styled-components": "^6.1.13", "swiper": "^6.7.5", "umi": "^2.3.2", "umi-plugin-react": "^1.2.3", "url-polyfill": "^1.0.10", "xlsx": "^0.18.5", "xlsx-oc": "^1.0.2"}, "devDependencies": {"@commitlint/cli": "^16.2.1", "@commitlint/config-conventional": "^16.2.1", "@umijs/fabric": "^1.2.0", "babel-eslint": "^8.1.2", "cross-env": "^5.1.1", "cross-port-killer": "^1.0.1", "enzyme": "^3.4.1", "eslint": "^5.0.0", "eslint-config-airbnb": "^17.0.0", "eslint-config-prettier": "^2.9.0", "eslint-plugin-babel": "^5.1.0", "eslint-plugin-compat": "^2.1.0", "eslint-plugin-import": "^2.8.0", "eslint-plugin-jsx-a11y": "^6.0.3", "eslint-plugin-markdown": "^1.0.0-beta.6", "eslint-plugin-react": "^7.0.1", "eslint-plugin-react-hooks": "^4.2.0", "gh-pages": "^1.0.0", "kblint": "0.0.9", "lint-staged": "^12.3.5", "mockjs": "^1.0.1-beta3", "node-xlsx": "^0.21.0", "prettier": "1.14.2", "pro-download": "^1.0.1", "redbox-react": "^1.5.0", "regenerator-runtime": "^0.12.0", "roadhog-api-doc": "^1.1.2", "stylelint": "^9.2.1", "stylelint-config-prettier": "^4.0.0", "stylelint-config-standard": "^18.0.0", "webpack-cli": "^3.1.2"}, "engines": {"node": ">=8.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 10"], "config": {"commitizen": {"path": "cz-conventional-changelog"}}}