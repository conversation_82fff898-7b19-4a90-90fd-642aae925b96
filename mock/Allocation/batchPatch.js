/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import mockjs from 'mockjs';

const { mock } = mockjs;

export default {
  'POST /v1/TotalDistribution/uploadGp/getSignerList': (req, res) => {
    res.send({
      code: 0,
      msg: '成功',
      data: {
        offcial: [],
        custom: [
          {
            pinYin: 'tarendaishou',
            disEditable: false,
            select: false,
            signType: '他人代收',
            id: 2,
            message: '他人代收',
          },
          {
            pinYin: 'danweish<PERSON><PERSON><PERSON>qi<PERSON><PERSON>',
            disEditable: false,
            select: false,
            signType: '单位收发室签收',
            id: 3,
            message: '单位收发室签收',
          },
          {
            pinYin: 'wuyedaishou',
            disEditable: false,
            select: false,
            signType: '物业代收',
            id: 4,
            message: '物业代收',
          },
          {
            pinYin: 'cuny<PERSON>zhanqianshou',
            disEditable: false,
            select: false,
            signType: '村邮站签收',
            id: 5,
            message: '村邮站签收',
          },
          {
            pinYin: 'xieyitouxiang',
            disEditable: false,
            select: false,
            signType: '协议投箱',
            id: 6,
            message: '协议投箱',
          },
          {
            pinYin: 'zitidiandaishou',
            disEditable: false,
            select: false,
            signType: '自提点代收',
            id: 7,
            message: '自提点代收',
          },
          {
            pinYin: 'toubaoguogui',
            disEditable: false,
            select: false,
            signType: '投包裹柜',
            id: 8,
            message: '投包裹柜',
          },
          {
            pinYin: 'xinbaoxiang',
            disEditable: false,
            select: false,
            signType: '信报箱',
            id: 9,
            message: '信报箱',
          },
          {
            pinYin: 'qita',
            disEditable: false,
            select: false,
            signType: '其他',
            id: 10,
            message: '其他',
          },
          {
            pinYin: 'duitaicangchuzhongxin',
            disEditable: false,
            select: false,
            signType: '对台仓储中心',
            id: 11,
            message: '对台仓储中心',
          },
        ],
        brand: 'ems',
        source: '',
      },
    });
  },

  'POST /v1/TotalDistribution/uploadGp/getBadWaybillType': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: [
        {
          badWayBillCode: 'A1',
          badWayBillDesc: '送无人',
          question_desc:
            '电话联系不上：无人接听、无法接通、忙音、关机等|电话错误：停机、空号、号码不存在、号码位数不对等|客户电话非本人|面单无电话/被红章、乱码遮盖|电联收件人说的方言听不懂/接通客户不说话/直接挂断/无法沟通。|语音提示输入分机/对端号码，面单无分机号/对端号码。|面单电话为隐私号码|此件面单地址、电话均为我司，核实我司内部人员未购买此件，请提供详细客户信息',
        },
        {
          badWayBillCode: 'A10',
          badWayBillDesc: '自提件',
          question_desc:
            '特殊地址：(如学校、部队、封闭单位等特殊地址必须要注明)，客户不愿出来取件。|特殊地址：(如学校、部队、封闭单位等特殊地址必须要注明)，已通知客户自取。|特殊地址：(如学校、部队、封闭单位等特殊地址必须要注明)，客户电话联系不上(停机、关机、无人接听、非本人等必须要注明联系不上具体情况)，请发件网点通知客户自行联系我司自提，并回复处理意见。|面单注明自提，客户不愿意自提，要求派送，请回复。|通知自提，但是一直未取。客户电话联系不上(停机、关机、无人接听、非本人等必须要注明联系不上具体情况)，请回复处理意见。|乡镇村组件，已通知客户自提。|快件投递至第三方驿站，已通知客户凭取件码自提，请知悉！|与客户达成共识，客户同意自提。自提地址：(必须登记自提地址)，自提地址工作时间：(如：上午9:00-下午6:00)联系电话：(自提点联系电话)，如需特殊处理请问题件回复我司。|乡镇村组件，客户电话联系不上(停机、关机、无人接听、非本人等必须要注明联系不上具体情况)，请回复处理意见',
        },
        {
          badWayBillCode: 'A12',
          badWayBillDesc: '客户要求改地址',
          question_desc:
            '客户要求改地址到：(必须提供含有省市区路名及号码段的详细地址)|客户要求改电话号码/姓名：(必须编辑客户号码或者姓名)|客户要求改地址，但并未告知所改地址，请贵司核实处理。',
        },
        {
          badWayBillCode: 'A16',
          badWayBillDesc: '客户放假，假后派送',
          question_desc: '国定工作日，(注明派送日期)派送。',
        },
        {
          badWayBillCode: 'A17',
          badWayBillDesc: '等待收件人仓库验货',
          question_desc: '等待收件人仓库验货',
        },
        {
          badWayBillCode: 'A18',
          badWayBillDesc: '第三方代收点无法派送',
          question_desc:
            '此件客户指定放置第三方，由于末端第三方已关停/已解除合作/不收快件，我司无法派送。|此件客户指定或面单地址为第三方，第三方无人签收/易丢件，无法派送。|此件客户指定或面单地址为第三方，要收取费用(费用标准)，我司无法派送。',
        },
        {
          badWayBillCode: 'A2',
          badWayBillDesc: '地址不详/错误',
          question_desc:
            '电话联系不上：无人接听、无法接通、忙音、关机等|电话错误：停机、空号、号码不存在、号码位数不对等|面单电话非本人|已联系客户确认地址为：(必须提供含有省市区路名及号码段的详细地址)。|面单信息二段码缺失，导致无法中转。请在24小时内提供含有省市区路名及号码段的详细地址。|面单无电话/被红章、乱码遮盖，请提供正确电话及地址|提示输入分机/对端号码，面单无分机号/对端号码，请提供正确电话及地址。|电话联系客户不愿告知/不清楚详细地址。|面单电话为隐私号码，请提供正确电话及地址|电联收件人说的方言听不懂/接通客户不说话/直接挂断/无法沟通。|双地址，确认地址为(必须提供含有省市区路名及号码段的详细地址)。',
        },
        {
          badWayBillCode: 'A5',
          badWayBillDesc: '客户拒收',
          question_desc:
            '客户因(不能备注无理由拒收，必须注明详细原因，如有验货必须注明验货结果)拒收。',
        },
        {
          badWayBillCode: 'A8',
          badWayBillDesc: '两次免费派送，第三次有偿派送',
          question_desc:
            '两次免费派送，客户要求第三次投递，我司安排下一频次派送，请转有偿派送费。|两次免费派送，第三次有偿派送，请确认回复。',
        },
        {
          badWayBillCode: 'A9',
          badWayBillDesc: '预约投递',
          question_desc:
            '已与客户预约派送，约定( 必须填写具体日期)派送。|此件为子母件，待到齐后派送。|中考期间无法派送，中考结束后( 必须填写具体日期)派送。|已与客户预约派送，约定等通知派送。|已与客户预约派送，约定下午派送。|已与客户预约派送，约定( 必须填写具体时间 )，第( 填写派送频次 )频次派送。',
        },
        {
          badWayBillCode: 'B1',
          badWayBillDesc: '超区',
          question_desc:
            '收件地址，属不派送区域，我司免费转下去派送。转出单号(  注明清楚转出单号)|收件地址，属不派送区域，已经联系客户，客户不愿意自提，要求派送，请回复处理意见。|收件地址超区，客户同意自提。自提地址：(必须登记自提地址)，自提地址工作时间：(如：上午9:00-下午6:00)联系电话：(自提点联系电话)，如需特殊处理请问题件回复我司。|收件地址超区，客户电话联系不上(停机、关机、无人接听、非本人等必须要注明联系不上具体情况)，请回复处理意见',
        },
        {
          badWayBillCode: 'B12',
          badWayBillDesc: '污染件',
          question_desc:
            '此件被污染，污染源单号(请填写污染源单号)，请回复如何处理。|此件被污染，污染源未找到，请回复如何处理。',
        },
        {
          badWayBillCode: 'B13',
          badWayBillDesc: '进仓/报关资料不详',
          question_desc:
            '未满足进仓条件，请提供(预约号、进仓编号、预约时间)。|此件是进仓件，将会按照预约时间(注明派送日期)进行派送。|报关件，缺少报关信息(必填缺失信息)请尽快处理。',
        },
        {
          badWayBillCode: 'B19',
          badWayBillDesc: '水果生鲜类破损',
          question_desc:
            '此件内物品名(请注明品名),到件外包装/内件异常(请注明外包装/内件异常情况)。',
        },
        {
          badWayBillCode: 'B2',
          badWayBillDesc: '无点',
          question_desc:
            '无点件，无法转单，请贵司及时回复处理方案|此件地址是无点区域，我司可转下去(  费用：XX   )到件后通知客户取件，请贵司及时回复处理方案。|此件地址是无点区域，我司可转下去派送，派件有所延迟，敬请谅解。',
        },
        {
          badWayBillCode: 'B21',
          badWayBillDesc: '弃件',
          question_desc:
            '生鲜类快件发件网点超时未回复/未明确回复，暂放到期弃件。|政府扣押内物为(           品名        )快件|此件已明显腐烂丧失实际价值，腐烂快件图片已上传。|生鲜快件无面单暂放5天无人认领，暂放到期弃件。',
        },
        {
          badWayBillCode: 'B3',
          badWayBillDesc: '错分/错发',
          question_desc: '记号笔/包牌错误,错发我司，下一频次转出',
        },
        {
          badWayBillCode: 'B4',
          badWayBillDesc: '破损/短少/内件不符',
          question_desc:
            '此件外包装破损，内件完好无损。|此件外包装破损，内件不符。|此件外包装破损，内物污染/潮湿。|此件外包装破损，内物损毁。|此件外包装破损，内物变形。|此件外包装破损，内物为空。|此件外包装破损，内物短少\n',
        },
        {
          badWayBillCode: 'B5',
          badWayBillDesc: '面单脱落/破损/磨损',
          question_desc:
            '面单脱落/破损/磨损，请尽快提供底单或回复收件人详细信息，以便转出派送。|内物为(填写内物)，未使用防水热敏纸，请尽快提供底单或回复收件人详细信息，以便转出派送。',
        },
        {
          badWayBillCode: 'B7',
          badWayBillDesc: '突发情况下不能及时送达',
          question_desc:
            '公司发生意外事件(详细说明事件情况)，派件有所延迟。|业务员发生交通事故，派件有所延迟。',
        },
        {
          badWayBillCode: 'B9',
          badWayBillDesc: '有单无货',
          question_desc: '到件只见面单，未见货物，请贵司尽快处理。',
        },
        {
          badWayBillCode: 'C1',
          badWayBillDesc: '自然灾害',
          question_desc:
            '暴雪，导致无法及时送达。|大雾，导致无法及时送达。|地震，导致无法及时送达。|台风/沙尘暴，导致无法及时送达。|暴雨，导致无法及时送达。|洪涝水灾，导致无法及时送达。|冰雹，导致无法及时送达。|泥石流，导致无法及时送达。',
        },
        {
          badWayBillCode: 'C2',
          badWayBillDesc: '政府干涉',
          question_desc: '因(道路限制，特殊节日，各种会议、比赛、快件被扣押等)原因导致无法及时派送',
        },
        {
          badWayBillCode: 'F1',
          badWayBillDesc: '违规快件',
          question_desc:
            '双面单(必须详细描述两个单号情况，请上传图片)|空包刷信誉：此件面单品名(   )  包装 (   )不相符，疑似空包刷信誉快件，核实客户：(  核实客户结果 ) ，贵司如有异议，24小时内提供证明至仲裁举证。|面单/单号重复使用(必须说明收件地址情况)|内件为(请填写品名或超长/超重/超宽)，属于禁运品|签单返还异常(请描述详细情况：未带回单面单，是否需要换单转出/无需要返回的签字单据，请明确需要返还的单据类型)，请核实并给予明确处理意见|面单条码不同单号(必须填写扫描后出现的不同单号，并上传面单图片)。|我司无此地址，请贵司自行联系核实客户准确地址并支付转件费。',
        },
      ],
    });
  },
  'POST /v1/TotalDistribution/uploadGp/getImportData': (req, res) => {
    const data = [];
    for (let i = 0; i < 5; i++) {
      data.push(['7' + Date.now() + i, '18211111110', '18211111111']);
    }

    res.send({
      code: 0,
      msg: '成功',
      data,
    });
  },
  'POST /v1/TotalDistribution/uploadGp/batchCheckWaybillListBrand': (req, res) => {
    const params = JSON.parse(req.body.data_list || JSON.stringify({}));
    const data = {};
    params.forEach(item => {
      const { waybillNo } = item;
      data[waybillNo] = mock({
        'brand|1': ['yt', 'yt'],
        'name|1': ['@cname', '@cname'],
        dispatched_at: '@datetime',
      });
    });
    res.send(
      mock({
        code: 0,
        msg: '成功',
        data,
      }),
    );
  },
  'POST /v1/TotalDistribution/uploadGp/batchGetWaybillNoNoticeList': (req, res) => {
    const params = JSON.parse(req.body.data_list || JSON.stringify({}));
    const data = {};
    params.forEach(item => {
      const { waybillNo } = item;
      data[waybillNo] = {
        code: '0',
        data: {
          waybillNo: '776381114804260',
          info: {
            intercept: '该快件是拦截件',
            weight: '重量0.06kg',
            realName: '实名寄递：未采集',
          },
          notice: [
            {
              directionContent: '【776381114804260】为拦截件，请打印并退改，拦截原因：退寄件人!',
              directionElement: {
                1: '确认',
              },
              directionFeature: '0',
              inDbAfterConfirm: '0',
              voicePrompt: '拦截件,请注意',
              'desc|1': ['拦截', '收件', ''],
              title: '拦',
              voice_name: 'lj',
              voice_file: 'https://upload.kuaidihelp.com/pda/notice/voice/lj.wav',
            },
          ],
        },
      };
    });

    res.send(
      mock({
        code: 0,
        msg: 'success',
        data,
      }),
    );
  },
  'POST /v1/TotalDistribution/uploadGp/batchUploadGp': (req, res) => {
    const params = JSON.parse(req.body.data_list || JSON.stringify({}));

    const data = {};

    params.forEach(item => {
      const { waybillNo } = item;
      data[waybillNo] = mock({
        'code|1': ['1', '0'],
        msg: '@cword(5,8)',
      });
    });
    res.send(
      mock({
        code: '0',
        data,
      }),
    );
  },
  'POST /v1/TotalDistribution/uploadGp/export': (req, res) => {
    res.send(
      mock({
        code: 0,
        data: {
          filePath:
            'http://upload.kuaidihelp.com/pda/cityup/excel/2024/04/08/parse-8bfe49323417f467ccd8dc273509cc25.xlsx',
        },
      }),
    );
  },
  'POST /v1/TotalDistribution/uploadGp/getLineList': (req, res) => {
    res.send({
      code: 0,
      msg: '成功',
      data: [
        {
          lineNo: 'TFS735010TO0735CP01',
          lineName: '湖南郴州市资兴市-资兴长平',
          endOrgCode: '735CP',
          endOrgName: '',
        },
        {
          lineNo: 'TFS0735CPTO73501001',
          lineName: '资兴长平-湖南郴州市资兴市',
          endOrgCode: '735010',
          endOrgName: '湖南省郴州市资兴市',
        },
        {
          lineNo: 'MOT735010TO73582001',
          lineName: '资兴公司-东江分部',
          endOrgCode: '735820',
          endOrgName: '湖南省郴州市资兴市东江镇',
        },
        {
          lineNo: 'MOT735820TO73501001',
          lineName: '东江分部-资兴公司',
          endOrgCode: '735010',
          endOrgName: '湖南省郴州市资兴市',
        },
        {
          lineNo: 'MOT735821TO73501001',
          lineName: '鲤鱼江分部-资兴公司',
          endOrgCode: '735010',
          endOrgName: '湖南省郴州市资兴市',
        },
        {
          lineNo: 'MOT735010TO73582101',
          lineName: '资兴公司-鲤鱼江分部',
          endOrgCode: '735821',
          endOrgName: '湖南省郴州市资兴市鲤鱼江镇',
        },
        {
          lineNo: 'MOT735822TO73501001',
          lineName: '资兴公司-兴宁分部',
          endOrgCode: '735010',
          endOrgName: '湖南省郴州市资兴市',
        },
        {
          lineNo: 'MOT735010TO73582201',
          lineName: '资兴公司-兴宁分部',
          endOrgCode: '735822',
          endOrgName: '湖南省郴州市资兴市兴宁镇',
        },
        {
          lineNo: 'MOT735829TO73501001',
          lineName: '三都镇分部-资兴公司',
          endOrgCode: '735010',
          endOrgName: '湖南省郴州市资兴市',
        },
        {
          lineNo: 'MOT735828TO73501001',
          lineName: '皮石乡分部-资兴公司',
          endOrgCode: '735010',
          endOrgName: '湖南省郴州市资兴市',
        },
        {
          lineNo: 'MOT735010TO73582901',
          lineName: '资兴公司-三都镇分部',
          endOrgCode: '735829',
          endOrgName: '湖南省郴州市资兴市三都镇',
        },
        {
          lineNo: 'MOT735010TO73582801',
          lineName: '资兴公司-皮石乡分部',
          endOrgCode: '735828',
          endOrgName: '湖南省郴州市资兴市彭市乡',
        },
        {
          lineNo: 'MOT735010TO73583201',
          lineName: '资兴公司-东坪分部',
          endOrgCode: '735832',
          endOrgName: '湖南省郴州市资兴市黄草镇',
        },
        {
          lineNo: 'MOT735832TO73501001',
          lineName: '东坪分部-资兴公司',
          endOrgCode: '735010',
          endOrgName: '湖南省郴州市资兴市',
        },
        {
          lineNo: 'MOT735010TO73583701',
          lineName: '资兴公司-清江乡分部',
          endOrgCode: '735837',
          endOrgName: '湖南省郴州市资兴市清江乡',
        },
        {
          lineNo: 'MOT735837TO73501001',
          lineName: '清江乡分部-资兴公司',
          endOrgCode: '735010',
          endOrgName: '湖南省郴州市资兴市',
        },
        {
          lineNo: 'TFS731901TO73501001',
          lineName: '长沙转运中心-湖南长沙资兴',
          endOrgCode: '735010',
          endOrgName: '湖南省郴州市资兴市',
        },
        {
          lineNo: 'TFS735010TO73190101',
          lineName: '湖南长沙资兴-长沙转运中心',
          endOrgCode: '731901',
          endOrgName: '长沙转运中心',
        },
        {
          lineNo: 'MOT735010TO73490101',
          lineName: '资兴公司-衡阳中心',
          endOrgCode: '734901',
          endOrgName: '衡阳转运中心',
        },
        {
          lineNo: 'MOT734951TO73501001',
          lineName: 'BW衡阳集散仓-资兴公司',
          endOrgCode: '735010',
          endOrgName: '湖南省郴州市资兴市',
        },
        {
          lineNo: 'MOT735010TO73495101',
          lineName: '资兴公司—BW衡阳集散仓',
          endOrgCode: '734951',
          endOrgName: 'BW衡阳集散仓',
        },
        {
          lineNo: 'MOT735010TO73190101',
          lineName: '湖南省郴州市资兴市-长沙转运中心',
          endOrgCode: '731901',
          endOrgName: '长沙转运中心',
        },
        {
          lineNo: 'MOT734901TO73501001',
          lineName: '衡阳中心-资兴公司',
          endOrgCode: '735010',
          endOrgName: '湖南省郴州市资兴市',
        },
      ],
    });
  },
  'POST /v1/TotalDistribution/uploadGp/getNextStationList': (req, res) => {
    res.send({
      code: 0,
      msg: '成功',
      data: [
        {
          station_name: '山东济南转运中心',
          station_code: '250500',
        },
        {
          station_name: '河南商丘中转部',
          station_code: '476001',
        },
      ],
    });
  },
  'POST /v1/TotalDistribution/uploadGp/getAutolineStatus': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: {
        status: 1,
      },
    });
  },
  'POST /Api/Automation/SortLine/getYdManagerInfoList': (req, res) => {
    res.send({
      code: 1,
      msg: '成功',
      data: [
        {
          id: 1,
          phone: '18583888862',
          list: [
            {
              networkName: '四川芦山县公司',
              empCode: '625500007',
              realName: '阿牛伍呷',
              phone: '18583888862',
              cardNo: '513432199506281417',
              gender: '1',
              cpCode: '625500',
              cpType: 4,
              age: '29',
              nickName: '',
              workingState: null,
              lb: '2',
              shortName: '芦山',
            },
          ],
          create_at: '2024-11-25 13:22:23',
        },
      ],
    });
  },
  'POST /Api/Automation/SortingZtPdaScanner/getInfoList': (req, res) => {
    res.send({
      code: 0,
      msg: '成功',
      data: [
        {
          id: '3',
          branch_code: '1212112',
          account: '12121',
          device_imei: '1212112',
          device_brand: '1121',
        },
      ],
    });
  },
  'POST /Api/Automation/SortingStoXzScanner/getInfoList': (req, res) => {
    res.send({
      code: 0,
      msg: '成功',
      data: [
        {
          id: '3',
          branch_code: '121',
          phone: '***********',
        },
      ],
    });
  },
  'POST /v1/TotalDistribution/uploadGp/getBrandsCanQuerySegmentCodes': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: {
        sto: 0,
        zt: 1,
        jt: 1,
        yd: 0,
        yt: 0,
      },
    });
  },
  'POST /v1/TotalDistribution/uploadGp/getDispatchAutoArriveStatus': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: {
        status: 1,
      },
    });
  },
  'POST /v1/TotalDistribution/uploadGp/getDispatchAutoArriveConfig': (req, res) => {
    res.send({
      code: 0,
      msg: '成功',
      data: {
        type: 2,
        data: [
          {
            brand: 'ems',
            status: '0',
            vehicle_no: '',
          },
          {
            brand: 'jt',
            status: '0',
            vehicle_no: '',
          },
          {
            brand: 'sto',
            status: '0',
            vehicle_no: '',
          },
          {
            brand: 'yd',
            status: '1',
            vehicle_no: '',
          },
          {
            brand: 'yt',
            status: '0',
            vehicle_no: 'CQ20230919',
          },
          {
            brand: 'zt',
            status: '0',
            vehicle_no: '',
          },
        ],
      },
    });
  },
  'POST /v1/TotalDistribution/uploadGp/setDispatchAutoArriveConfig': (req, res) => {
    res.send({
      code: 0,
      msg: '成功',
      data: null,
    });
  },
  'POST /v1/TotalDistribution/uploadGp/getSignAutoDispatchConfig': (req, res) => {
    res.send({
      code: 0,
      msg: '成功',
      data: {
        type: 2,
        data: [
          {
            brand: 'ems',
            status: '0',
          },
          {
            brand: 'jt',
            status: '1',
          },
          {
            brand: 'sto',
            status: '1',
          },
          {
            brand: 'yd',
            status: '1',
          },
          {
            brand: 'yt',
            status: '0',
          },
          {
            brand: 'zt',
            status: '0',
          },
        ],
      },
    });
  },
  'POST /v1/TotalDistribution/uploadGp/setSignAutoDispatchConfig': (req, res) => {
    res.send({
      code: 0,
      msg: '成功',
      data: null,
    });
  },
  'POST /v1/TotalDistribution/uploadGp/getAllCourierNameMap': (req, res) => {
    res.send({
      code: 0,
      msg: '成功',
      data: {
        18012345678: '张三',
        18290823142: '李四',
        15123213112: '王二',
        18211111110: '司机',
        18211111111: '荣誉',
      },
    });
  },
  'POST /v1/TotalDistribution/uploadGp/getSpecialCheckConfig': (req, res) => {
    res.send({
      code: 0,
      msg: '成功',
      data: {
        1: true,
        5: true,
      },
    });
  },
  'POST /v1/TotalDistribution/uploadGp/setSpecialCheckConfig': (req, res) => {
    res.send({
      code: 0,
      msg: '成功',
      data: null,
    });
  },
};
