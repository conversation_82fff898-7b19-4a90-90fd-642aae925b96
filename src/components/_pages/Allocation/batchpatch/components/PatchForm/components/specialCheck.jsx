/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import React, { useMemo } from 'react';
import { Icon, Modal, Radio, Switch } from 'antd';
import { setSpecialCheckConfig } from '@/services/batchPatch';

const SpecialCheck = props => {
  const { scanValue, specialCheckConfig = {}, pageData, updatePageData, runSpecialCheckConfig } = props;

  // 特殊件检查开关
  const onSpecialSwitch = async event => {
    console.log('event===>', event);
    if (!event) {
      Modal.confirm({
        centered: true,
        title: '特殊件检查',
        content: (
          <div>
            <div>确定关闭特殊件检查么?关闭后，本次到件上传不检查特殊件</div>
            <Radio>每次提交运单号均不检查特殊件</Radio>
          </div>
        ),
        okText: '确定关闭',
        cancelText: '取消',
        onOk: () => {
          handleOk(false);
        },
      });
    } else {
      await setSpecialCheckConfig({ [scanValue]: false });
      await runSpecialCheckConfig();
      handleOk(true);
    }
  };

  const handleOk = e => {
    updatePageData(prev => {
      const { TE_SHU_JIAN_JIAN_CHA, ...rest } = prev;
      return {
        ...rest,
        TE_SHU_JIAN_JIAN_CHA: {
          ...TE_SHU_JIAN_JIAN_CHA,
          [scanValue]: e,
        },
      };
    });
  };

  const checked = useMemo(
    () => {
      return specialCheckConfig[scanValue] ? false : pageData.TE_SHU_JIAN_JIAN_CHA[scanValue];
    },
    [pageData.TE_SHU_JIAN_JIAN_CHA, scanValue, specialCheckConfig],
  );

  return (
    <>
      <Switch checked={checked} onChange={onSpecialSwitch} />
      {specialCheckConfig[scanValue] && (
        <Icon type="lock" style={{ fontSize: 20, marginLeft: 10 }} />
      )}
    </>
  );
};

export default SpecialCheck;
