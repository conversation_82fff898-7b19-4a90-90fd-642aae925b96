/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { useDebounceEffect, useUpdateEffect } from 'ahooks';
import { Icon, Modal, Row, Tag, message } from 'antd';
import React, { useState, useMemo, useEffect } from 'react';
import moment from 'moment';
import { MAX_ORDER_LENGTH, useOrderUpload } from './useOrderUpload';
import { transformBrand } from './transformBrand';
import { useIndexedDBState } from './useIndexedDBState';

export function usePatchTable(props) {
  const { pageData, toggleLoading, updatePageData } = props;
  const { scanDataKey, scanLabel, scanValue, list, addListStatus, FA_JIAN_BRAND } = pageData;

  const [selectRows, setSelectRows] = useState([]);
  const [localData = [], setLocalData] = useIndexedDBState(scanDataKey);

  const { loading, getStatus, uploadOrders } = useOrderUpload({ toggleLoading });

  // IndexedDB 中已经过滤了2小时内的数据，这里直接使用
  const formatLocalData = localData;

  const isShowErrMsg = useMemo(() => formatLocalData.some(i => !!i.errMsg), [formatLocalData]);

  const columns = useMemo(
    () =>
      [
        {
          title: '单号',
          dataIndex: 'waybill',
          key: 'waybill',
          render: (val, item) => (
            <Row align="middle" justify="center">
              <div>{val}</div>
              {!item.init && <Icon type="loading" style={{ color: '#1890FF' }} />}
              {!!(item?.desc && item?.desc?.length) &&
                item.desc.map(i => (
                  <Tag key={i} color="red">
                    {i}
                  </Tag>
                ))}
            </Row>
          ),
        },
        {
          title: '品牌',
          dataIndex: 'brand',
          key: 'brand',
          render: val => (val == 'ems' ? 'EMS' : transformBrand(val)),
        },
        {
          title: '扫描员',
          dataIndex: 'name',
          key: 'name',
          render: (_, item) => `${item.courier_name || ''} ${item.courier_phone || ''}`,
        },
        {
          title: '上次派件时间',
          dataIndex: 'dispatched_at',
          key: 'dispatched_at',
          hide: scanValue != '17',
        },
        {
          title: '新派件员',
          dataIndex: 'name',
          key: 'name',
          render: (_, item) =>
            `${item.new_dispatch_courier_name || ''} ${item.new_dispatch_courier_phone || ''}`,
          hide: scanValue != '17',
        },
        {
          title: '派件员',
          dataIndex: 'phone',
          key: 'phone',
          hide: !['2', '5'].includes(scanValue),
          render: (_, item) => `${item.operatorCourier_name || ''} ${item.operatorCourier || ''}`,
        },
        {
          title: '签收类型',
          dataIndex: 'signType',
          key: 'signType',
          hide: scanValue != '3',
        },
        {
          title: '问题类型',
          dataIndex: 'badWayBillDesc',
          key: 'badWayBillDesc',
          hide: scanValue != '4',
        },
        {
          title: '下一站',
          dataIndex: 'next_station_name',
          key: 'next_station_name',
          hide: scanValue != '6',
        },
        {
          title: '车签号',
          dataIndex: 'vehicle_no',
          key: 'vehicle_no',
          hide: !(scanValue == '6' && FA_JIAN_BRAND == 'yt'),
        },
        {
          title: '线路',
          dataIndex: 'line_name',
          key: 'line_name',
          hide: !(scanValue == '6' && FA_JIAN_BRAND == 'yt'),
        },
        {
          title: '发车凭证',
          dataIndex: 'voucher_no',
          key: 'voucher_no',
          hide: !(scanValue == '6' && FA_JIAN_BRAND == 'yd'),
        },
        {
          title: '扫描时间',
          dataIndex: 'scan_time',
          key: 'scan_time',
        },
        {
          title: '失败原因',
          dataIndex: 'errMsg',
          key: 'errMsg',
          hide: !isShowErrMsg,
          render: val => <span style={{ color: 'red' }}>{val}</span>,
        },
      ].filter(item => !item.hide),
    [scanValue, isShowErrMsg, FA_JIAN_BRAND],
  );

  // 上传
  const onUpload = async () => {
    if (loading) return;
    const uploadList = formatLocalData.filter(i => selectRows.includes(i.waybill));
    const result = await uploadOrders(uploadList);

    // 处理点选列表。
    const forSelectRow = result.filter(i => i.errMsg).map(i => i.waybill);
    setSelectRows([...forSelectRow]);
    // 处理提交的数据，提交成功的删掉，提交失败的同步失败原因
    const forLocalData = formatLocalData
      .map(i => {
        const item = result.find(o => o.waybill == i.waybill);
        if (item) {
          if (item.errMsg) {
            return {
              ...i,
              ...item,
            };
          }
          return '';
        }
        return i;
      })
      .filter(i => !!i);

    setLocalData([...forLocalData]);
  };

  // 删除
  const onDelete = () => {
    Modal.confirm({
      centered: true,
      title: '提示',
      content: '确定删除？',
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        const format = formatLocalData.filter(i => !selectRows.includes(i.waybill));
        setLocalData(format);
        setSelectRows([]);
        message.success('删除成功');
      },
    });
  };

  // 添加新单号
  useUpdateEffect(
    () => {
      // 去重
      const formatList = list
        .filter(i => !formatLocalData.some(o => o.waybill == i.waybill))
        .map(i => ({
          ...i,
          init: false,
        }));

      const localList = [...formatList, ...formatLocalData];
      setLocalData(localList);
      getOrderStatus(localList);
    },
    [addListStatus],
  );

  // 获取单号状态方法
  const getOrderStatus = async lis => {
    console.info('getOrderStatus====>lis', lis);
    const list_ = lis.filter(i => {
      if (i.TE_SHU_JIAN_JIAN_CHA != undefined) {
        return i.TE_SHU_JIAN_JIAN_CHA !== '0';
      }
      return !i.init;
    });

    console.info('getOrderStatus====>filter', list_);

    const orders = list_.length ? await getStatus(list_) : [];
    const formatList = lis
      .map(i => {
        const obj = orders.find(o => o.waybill == i.waybill) || {};
        return {
          ...i,
          ...obj,
          init: true,
        };
      })
      .filter(item => {
        const { scan_time } = item;
        const isAfter = moment().diff(moment(scan_time), 'hours') < 2;
        return isAfter;
      });
    setLocalData([...formatList]);
  };

  useEffect(() => {
    // 只有当 localData 存在且不为空时才调用 getOrderStatus
    if (localData && localData.length > 0) {
      getOrderStatus(localData);
    }
  }, [localData.length]);

  // 切换类型后，清空选择项。
  useUpdateEffect(
    () => {
      setSelectRows([]);
    },
    [scanDataKey],
  );

  useDebounceEffect(
    () => {
      if (scanDataKey == 'fajian') {
        const brand = formatLocalData[0]?.brand || '';
        if ((brand || FA_JIAN_BRAND) && brand != FA_JIAN_BRAND) {
          updatePageData({
            FA_JIAN_BRAND: brand,
          });
        }
      } else {
        updatePageData({
          FA_JIAN_BRAND: '',
        });
      }
    },
    [scanDataKey, formatLocalData.length, FA_JIAN_BRAND],
    { wait: 300 },
  );

  const onCheckedAll = event => {
    const { checked } = event.target;
    if (checked) {
      const checkedList = formatLocalData.map(i => i.waybill);
      if (checkedList.length > MAX_ORDER_LENGTH) {
        setSelectRows(checkedList.slice(0, MAX_ORDER_LENGTH));
      } else {
        setSelectRows(checkedList);
      }
    } else {
      setSelectRows([]);
    }
  };
  return {
    tableTitle: scanLabel,
    selectRows,
    setSelectRows,
    localData: formatLocalData,
    onUpload,
    onDelete,
    columns,
    loading,
    onCheckedAll,
  };
}
