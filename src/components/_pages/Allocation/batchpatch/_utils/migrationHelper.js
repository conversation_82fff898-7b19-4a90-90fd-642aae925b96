/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 *
 * 数据迁移工具：将 localStorage 数据迁移到 IndexedDB
 */

import moment from 'moment';
import { db } from './db';

// 扫描类型列表
const SCAN_TYPES = ['daojian', 'paijian', 'qianshou', 'wentijian', 'daopai', 'fajian', 'gaipai'];

/**
 * 迁移 localStorage 数据到 IndexedDB
 */
export async function migrateLocalStorageToIndexedDB() {
  try {
    console.log('开始迁移 localStorage 数据到 IndexedDB...');

    let totalMigrated = 0;

    // eslint-disable-next-line no-restricted-syntax
    for (const scanType of SCAN_TYPES) {
      try {
        // 从 localStorage 读取数据
        const localStorageKey = scanType;
        const localData = localStorage.getItem(localStorageKey);

        if (localData) {
          const parsedData = JSON.parse(localData);

          if (Array.isArray(parsedData) && parsedData.length > 0) {
            // 过滤2小时内的数据
            const validData = parsedData.filter(item => {
              const { scan_time } = item;
              const isAfter = moment().diff(moment(scan_time), 'hours') < 2;
              return isAfter;
            });

            if (validData.length > 0) {
              // 添加 scanType 字段并迁移到 IndexedDB
              const dataWithScanType = validData.map(item => ({
                ...item,
                scanType,
              }));

              await db.scanData.bulkPut(dataWithScanType);
              totalMigrated += validData.length;

              console.log(`✓ 迁移 ${scanType} 数据: ${validData.length} 条`);
            }
          }

          // 迁移完成后清除 localStorage 数据
          localStorage.removeItem(localStorageKey);
        }
      } catch (error) {
        console.error(`迁移 ${scanType} 数据失败:`, error);
      }
    }

    console.log(`✓ 数据迁移完成，共迁移 ${totalMigrated} 条数据`);
    return totalMigrated;
  } catch (error) {
    console.error('数据迁移失败:', error);
    return 0;
  }
}

/**
 * 检查是否需要迁移数据
 */
export function shouldMigrate() {
  return SCAN_TYPES.some(scanType => {
    const localData = localStorage.getItem(scanType);
    return localData && localData !== 'null' && localData !== '[]';
  });
}

/**
 * 自动迁移（在应用启动时调用）
 */
export async function autoMigrate() {
  if (shouldMigrate()) {
    console.log('检测到 localStorage 中有数据，开始自动迁移...');
    await migrateLocalStorageToIndexedDB();
  }
}
