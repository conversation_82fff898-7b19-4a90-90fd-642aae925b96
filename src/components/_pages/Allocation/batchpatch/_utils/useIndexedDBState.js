/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { useEffect, useCallback, useMemo } from 'react';
import { useLiveQuery } from 'dexie-react-hooks';
import moment from 'moment';
import { db } from './db';
import { autoMigrate } from './migrationHelper';

/**
 * 使用 IndexedDB 替代 localStorage 的自定义 hook
 * @param {string} scanType - 扫描类型，对应原来的 scanDataKey
 * @returns {[Array, Function]} - [数据, 设置数据的函数]
 */
export function useIndexedDBState(scanType) {
  // 首次使用时自动迁移数据
  useEffect(() => {
    autoMigrate();
  }, []);

  // 使用 dexie-react-hooks 的 useLiveQuery 实时查询数据
  const rawData = useLiveQuery(
    async () => {
      if (!scanType) return [];

      // 查询指定扫描类型的数据，不在这里过滤时间
      const result = await db.scanData
        .where('scanType')
        .equals(scanType)
        .toArray();

      return result;
    },
    [scanType],
  );

  // 使用 useMemo 来稳定时间过滤逻辑，避免频繁重新计算
  const data = useMemo(() => {
    if (!rawData || !Array.isArray(rawData)) return [];

    // 只返回2小时内的数据，与原逻辑保持一致
    const filteredData = rawData.filter(item => {
      const { scan_time } = item;
      const isAfter = moment().diff(moment(scan_time), 'hours') < 2;
      return isAfter;
    });

    // 按运单号排序，确保数据顺序稳定
    return filteredData.sort((a, b) => (a.waybill || '').localeCompare(b.waybill || ''));
  }, [rawData]);

  // 设置数据的函数
  const setData = useCallback(
    async newData => {
      if (!scanType) return;

      try {
        // 先清除该扫描类型的所有数据
        await db.scanData
          .where('scanType')
          .equals(scanType)
          .delete();

        // 添加新数据，为每个数据项添加 scanType 字段
        if (newData && newData.length > 0) {
          const dataWithScanType = newData.map(item => ({
            ...item,
            scanType,
          }));
          await db.scanData.bulkPut(dataWithScanType);
        }
      } catch (error) {
        console.error('IndexedDB 操作失败:', error);
      }
    },
    [scanType],
  );

  return [data || [], setData];
}
